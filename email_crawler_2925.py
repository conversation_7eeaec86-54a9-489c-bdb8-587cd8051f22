#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
2925邮箱邮件爬取脚本
登录后爬取所有邮件内容
"""

import time
import json
import csv
import os
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.edge.service import Service as EdgeService
from selenium.webdriver.edge.options import Options as EdgeOptions
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.microsoft import EdgeChromiumDriverManager
import config

class Email2925Crawler:
    def __init__(self):
        self.login_url = config.LOGIN_URL
        self.email = config.EMAIL
        self.password = config.PASSWORD
        self.driver = None
        self.emails = []

    def setup_driver(self):
        """设置Edge浏览器驱动"""
        try:
            print("正在初始化Edge浏览器...")
            edge_options = EdgeOptions()

            # 基本选项
            edge_options.add_argument("--no-sandbox")
            edge_options.add_argument("--disable-dev-shm-usage")
            edge_options.add_argument("--disable-gpu")
            edge_options.add_argument("--disable-web-security")
            edge_options.add_argument("--ignore-certificate-errors")
            edge_options.add_argument("--disable-extensions")
            edge_options.add_argument(f"--window-size={config.WINDOW_SIZE}")
            edge_options.page_load_strategy = 'eager'

            # 获取Edge驱动
            driver_manager = EdgeChromiumDriverManager()
            driver_path = driver_manager.install()
            print(f"使用Edge驱动: {driver_path}")

            service = EdgeService(driver_path)
            self.driver = webdriver.Edge(service=service, options=edge_options)
            self.driver.implicitly_wait(config.IMPLICIT_WAIT)
            print("Edge浏览器驱动初始化成功")
            return True
        except Exception as e:
            print(f"Edge浏览器驱动初始化失败: {e}")
            return False

    def login(self):
        """执行自动登录"""
        try:
            print("正在打开登录页面...")
            self.driver.set_page_load_timeout(20)
            self.driver.get(self.login_url)

            # 等待页面加载
            WebDriverWait(self.driver, 15).until(
                EC.presence_of_element_located((By.ID, "ipt_account"))
            )
            time.sleep(3)

            print("正在输入登录信息...")

            # 输入邮箱
            email_input = self.driver.find_element(By.ID, "ipt_account")
            email_input.clear()
            email_input.send_keys(self.email)

            # 输入密码
            password_input = self.driver.find_element(By.ID, "ipt_password")
            password_input.clear()
            password_input.send_keys(self.password)

            # 处理复选框
            try:
                icheck_wrappers = self.driver.find_elements(By.CSS_SELECTOR, ".icheckbox_minimal")
                for wrapper in icheck_wrappers:
                    try:
                        wrapper.click()
                    except:
                        pass
            except:
                pass

            # 点击登录按钮
            login_button = self.driver.find_element(By.ID, "btn_login")
            login_button.click()

            # 等待登录完成
            print("等待登录完成...")
            time.sleep(5)

            # 检查是否成功登录到邮箱主页
            try:
                WebDriverWait(self.driver, 10).until(
                    lambda driver: "2925.com/#/" in driver.current_url
                )
                print("登录成功！")
                return True
            except TimeoutException:
                print("登录失败或页面跳转超时")
                return False

        except Exception as e:
            print(f"登录过程中出现错误: {e}")
            return False

    def navigate_to_inbox(self):
        """导航到收件箱"""
        try:
            print("正在进入收件箱...")

            # 等待页面完全加载
            time.sleep(3)

            # 查找收件箱链接或按钮
            inbox_selectors = [
                "//a[contains(text(), '收件箱')]",
                "//div[contains(text(), '收件箱')]",
                "//span[contains(text(), '收件箱')]",
                "//*[@class='收件箱']",
                "//a[contains(@href, 'inbox')]"
            ]

            inbox_element = None
            for selector in inbox_selectors:
                try:
                    inbox_element = self.driver.find_element(By.XPATH, selector)
                    if inbox_element.is_displayed():
                        break
                except:
                    continue

            if inbox_element:
                inbox_element.click()
                print("已点击收件箱")
                time.sleep(3)
                return True
            else:
                print("未找到收件箱链接，可能已经在收件箱页面")
                return True

        except Exception as e:
            print(f"导航到收件箱失败: {e}")
            return False

    def get_email_list(self):
        """获取邮件列表"""
        try:
            print("正在获取邮件列表...")

            # 等待邮件列表加载
            time.sleep(3)

            # 查找邮件列表元素
            email_list_selectors = [
                ".email-list .email-item",
                ".mail-list .mail-item",
                ".message-list .message-item",
                "tr[class*='mail']",
                "div[class*='email']",
                ".list-item",
                "tbody tr"
            ]

            email_elements = []
            for selector in email_list_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        email_elements = elements
                        print(f"使用选择器 '{selector}' 找到 {len(elements)} 个邮件元素")
                        break
                except:
                    continue

            if not email_elements:
                # 如果没找到，尝试通用方法
                print("尝试查找所有可能的邮件元素...")
                all_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), '@') or contains(text(), '主题') or contains(text(), '发件人')]")
                email_elements = all_elements[:20]  # 限制数量避免过多
                print(f"找到 {len(email_elements)} 个可能的邮件元素")

            return email_elements

        except Exception as e:
            print(f"获取邮件列表失败: {e}")
            return []

    def extract_email_info(self, email_element):
        """从邮件元素中提取信息"""
        try:
            email_info = {
                'sender': '',
                'subject': '',
                'date': '',
                'content': '',
                'read_status': 'unknown'
            }

            # 尝试提取发件人
            try:
                sender_element = email_element.find_element(By.XPATH, ".//*[contains(@class, 'sender') or contains(@class, 'from')]")
                email_info['sender'] = sender_element.text.strip()
            except:
                # 尝试查找包含@符号的文本
                try:
                    text_content = email_element.text
                    import re
                    email_match = re.search(r'[\w\.-]+@[\w\.-]+\.\w+', text_content)
                    if email_match:
                        email_info['sender'] = email_match.group()
                except:
                    pass

            # 尝试提取主题
            try:
                subject_element = email_element.find_element(By.XPATH, ".//*[contains(@class, 'subject') or contains(@class, 'title')]")
                email_info['subject'] = subject_element.text.strip()
            except:
                # 如果没有找到特定的主题元素，使用整个元素的文本
                try:
                    email_info['subject'] = email_element.text.strip()[:100]  # 限制长度
                except:
                    pass

            # 尝试提取日期
            try:
                date_element = email_element.find_element(By.XPATH, ".//*[contains(@class, 'date') or contains(@class, 'time')]")
                email_info['date'] = date_element.text.strip()
            except:
                pass

            # 检查是否已读
            try:
                if 'unread' in email_element.get_attribute('class').lower():
                    email_info['read_status'] = 'unread'
                elif 'read' in email_element.get_attribute('class').lower():
                    email_info['read_status'] = 'read'
            except:
                pass

            return email_info

        except Exception as e:
            print(f"提取邮件信息失败: {e}")
            return None

    def crawl_emails_with_content(self, count=3):
        """爬取邮件并获取详细内容"""
        try:
            print(f"开始爬取最新 {count} 条邮件（包含详细内容）...")

            # 等待页面加载
            print("等待邮箱主页加载...")
            time.sleep(3)

            # 打印调试信息
            print(f"当前URL: {self.driver.current_url}")
            print(f"页面标题: {self.driver.title}")

            # 关键步骤：点击收件箱进入邮件列表
            if not self.click_inbox():
                print("无法进入收件箱")
                return False

            # 等待邮件列表加载
            print("等待邮件列表加载...")
            time.sleep(3)

            # 逐个处理邮件，每次重新获取列表避免stale element reference
            processed_count = 0

            for i in range(count):
                try:
                    print(f"处理第 {i+1} 封邮件...")

                    # 每次重新获取邮件列表
                    email_elements = self.get_email_list_fast()

                    if not email_elements or len(email_elements) <= i:
                        print(f"  没有更多邮件可处理")
                        break

                    # 获取当前要处理的邮件元素
                    email_element = email_elements[i]

                    # 调试信息：显示元素的基本信息
                    try:
                        element_text = email_element.text.strip()[:100]
                        print(f"  元素文本预览: {element_text}...")
                    except:
                        print(f"  无法获取元素文本")

                    # 先提取基本信息
                    email_info = self.extract_email_info_fast(email_element)

                    if email_info:
                        # 尝试点击邮件获取详细内容
                        try:
                            detailed_content = self.get_email_detail_content(email_element, i)
                            if detailed_content:
                                email_info['content'] = detailed_content
                                email_info['has_detail'] = True
                            else:
                                email_info['has_detail'] = False
                        except Exception as e:
                            print(f"  获取详细内容失败: {e}")
                            email_info['has_detail'] = False

                        self.emails.append(email_info)
                        processed_count += 1
                        print(f"✓ 已提取: {email_info['sender']} - {email_info['subject'][:50]}...")

                        # 确保返回到邮件列表页面
                        self.ensure_back_to_inbox()

                    else:
                        print(f"✗ 第 {i+1} 封邮件提取失败")

                except Exception as e:
                    print(f"✗ 处理第 {i+1} 封邮件失败: {e}")
                    # 尝试返回邮件列表
                    self.ensure_back_to_inbox()
                    continue

            print(f"\n邮件爬取完成！共获取 {processed_count} 封邮件")
            return True

        except Exception as e:
            print(f"爬取邮件失败: {e}")
            return False

    def ensure_back_to_inbox(self):
        """确保返回到收件箱邮件列表页面"""
        try:
            print("  确保返回收件箱...")

            # 检查当前URL是否包含收件箱相关信息
            current_url = self.driver.current_url
            if "2925.com/#/" in current_url and "mail" not in current_url:
                # 可能已经在主页，需要重新点击收件箱
                time.sleep(2)
                if not self.click_inbox():
                    print("  返回收件箱失败")
                    return False
            else:
                # 尝试使用浏览器后退
                try:
                    self.driver.back()
                    time.sleep(2)

                    # 检查是否成功返回
                    new_url = self.driver.current_url
                    if "2925.com/#/" in new_url:
                        # 重新点击收件箱
                        if not self.click_inbox():
                            print("  返回收件箱失败")
                            return False
                except:
                    # 如果后退失败，直接导航到主页再点击收件箱
                    try:
                        self.driver.get("https://www.2925.com/#/")
                        time.sleep(3)
                        if not self.click_inbox():
                            print("  返回收件箱失败")
                            return False
                    except:
                        return False

            # 等待邮件列表加载
            time.sleep(2)
            print("  ✓ 成功返回收件箱")
            return True

        except Exception as e:
            print(f"  返回收件箱失败: {e}")
            return False

    def crawl_emails_fast(self, count=3):
        """快速爬取最新的邮件（不点击进入详情页）"""
        try:
            print(f"开始快速爬取最新 {count} 条邮件...")

            # 等待页面加载
            print("等待邮箱主页加载...")
            time.sleep(3)

            # 打印调试信息
            print(f"当前URL: {self.driver.current_url}")
            print(f"页面标题: {self.driver.title}")

            # 关键步骤：点击收件箱进入邮件列表
            if not self.click_inbox():
                print("无法进入收件箱")
                return False

            # 等待邮件列表加载
            print("等待邮件列表加载...")
            time.sleep(3)

            # 获取邮件列表
            email_elements = self.get_email_list_fast()

            if not email_elements:
                print("未找到邮件元素")
                return False

            # 只处理前count个邮件
            email_elements = email_elements[:count]
            print(f"找到 {len(email_elements)} 个邮件，开始快速提取信息...")

            for i, email_element in enumerate(email_elements):
                try:
                    print(f"处理第 {i+1} 封邮件...")

                    # 快速提取邮件基本信息（不点击进入详情）
                    email_info = self.extract_email_info_fast(email_element)

                    if email_info:
                        self.emails.append(email_info)
                        print(f"✓ 已提取: {email_info['sender']} - {email_info['subject'][:50]}...")
                    else:
                        print(f"✗ 第 {i+1} 封邮件提取失败")

                except Exception as e:
                    print(f"✗ 处理第 {i+1} 封邮件失败: {e}")
                    continue

            print(f"\n快速邮件爬取完成！共获取 {len(self.emails)} 封邮件")
            return True

        except Exception as e:
            print(f"快速爬取邮件失败: {e}")
            return False

    def click_inbox(self):
        """点击收件箱进入邮件列表"""
        try:
            print("正在查找并点击收件箱...")

            # 使用已知有效的选择器
            inbox_selector = "//span[contains(text(), '收件箱')]"

            try:
                element = self.driver.find_element(By.XPATH, inbox_selector)
                if element and element.is_displayed():
                    print(f"找到收件箱元素: {inbox_selector}")
                    element.click()
                    print("✓ 成功点击收件箱")
                    return True
            except Exception as e:
                print(f"使用主选择器失败: {e}")

                # 备用选择器
                backup_selectors = [
                    "//li[contains(text(), '收件箱')]",
                    ".inbox",
                    "#inbox",
                    "[data-folder='inbox']",
                    "a[href*='inbox']"
                ]

                for selector in backup_selectors:
                    try:
                        if selector.startswith("//"):
                            element = self.driver.find_element(By.XPATH, selector)
                        else:
                            element = self.driver.find_element(By.CSS_SELECTOR, selector)

                        if element and element.is_displayed():
                            print(f"找到收件箱元素: {selector}")
                            element.click()
                            print("✓ 成功点击收件箱")
                            return True

                    except Exception as e:
                        continue

            print("❌ 未找到收件箱链接")
            return False

        except Exception as e:
            print(f"点击收件箱失败: {e}")
            return False

    def get_email_list_fast(self):
        """快速获取邮件列表"""
        try:
            print("正在快速获取邮件列表...")

            # 尝试多种选择器，按优先级排序
            selectors = [
                "tbody tr",  # 之前成功的选择器
                "table tr:not(:first-child)",
                ".mail-list tr",
                ".email-list tr",
                "tr[class*='mail']",
                "tr[class*='email']",
                ".list-item",
                "div[class*='mail']",
                "div[class*='email']"
            ]

            for selector in selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements and len(elements) > 0:
                        # 过滤掉可能的表头或空元素
                        valid_elements = []
                        for elem in elements:
                            try:
                                text = elem.text.strip()
                                if text and len(text) > 10:  # 有实际内容
                                    valid_elements.append(elem)
                            except:
                                continue

                        if valid_elements:
                            print(f"✓ 使用选择器 '{selector}' 找到 {len(valid_elements)} 个有效邮件元素")
                            return valid_elements
                except Exception as e:
                    print(f"选择器 '{selector}' 失败: {e}")
                    continue

            # 如果都没找到，尝试查找包含@符号的元素
            print("尝试查找包含邮箱地址的元素...")
            try:
                email_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), '@')]")
                if email_elements:
                    print(f"通过邮箱地址找到 {len(email_elements)} 个元素")
                    return email_elements[:10]  # 限制数量
            except Exception as e:
                print(f"查找邮箱地址元素失败: {e}")

            return []

        except Exception as e:
            print(f"快速获取邮件列表失败: {e}")
            return []

    def get_email_detail_content(self, email_element, index):
        """点击邮件获取详细内容"""
        try:
            print(f"  尝试获取第 {index+1} 封邮件的详细内容...")

            # 保存当前窗口句柄
            original_window = self.driver.current_window_handle
            original_url = self.driver.current_url

            # 尝试点击邮件
            try:
                # 滚动到元素可见
                self.driver.execute_script("arguments[0].scrollIntoView(true);", email_element)
                time.sleep(1)

                # 尝试多种点击方式
                click_success = False

                # 方法1：直接点击
                try:
                    email_element.click()
                    click_success = True
                    print(f"  ✓ 直接点击成功")
                except Exception as e:
                    print(f"  直接点击失败: {e}")

                # 方法2：JavaScript点击
                if not click_success:
                    try:
                        self.driver.execute_script("arguments[0].click();", email_element)
                        click_success = True
                        print(f"  ✓ JavaScript点击成功")
                    except Exception as e:
                        print(f"  JavaScript点击失败: {e}")

                # 方法3：查找链接并点击
                if not click_success:
                    try:
                        link = email_element.find_element(By.TAG_NAME, "a")
                        link.click()
                        click_success = True
                        print(f"  ✓ 链接点击成功")
                    except Exception as e:
                        print(f"  链接点击失败: {e}")

                if not click_success:
                    print(f"  ✗ 所有点击方法都失败")
                    return None

                time.sleep(3)

                # 检查是否打开了新窗口或页面
                current_windows = self.driver.window_handles
                if len(current_windows) > 1:
                    # 切换到新窗口
                    for window in current_windows:
                        if window != original_window:
                            self.driver.switch_to.window(window)
                            break

                # 尝试获取邮件内容
                content_selectors = [
                    ".email-content",
                    ".mail-content",
                    ".message-content",
                    ".email-body",
                    ".mail-body",
                    ".message-body",
                    "[class*='content']",
                    "[class*='body']",
                    "iframe[name='contentFrame']",
                    "iframe[id*='content']"
                ]

                email_content = ""

                # 尝试各种选择器
                for selector in content_selectors:
                    try:
                        if selector.startswith("iframe"):
                            # 处理iframe
                            iframe = self.driver.find_element(By.CSS_SELECTOR, selector)
                            self.driver.switch_to.frame(iframe)
                            email_content = self.driver.find_element(By.TAG_NAME, "body").text
                            self.driver.switch_to.default_content()
                        else:
                            element = self.driver.find_element(By.CSS_SELECTOR, selector)
                            email_content = element.text.strip()

                        if email_content and len(email_content) > 20:
                            print(f"  ✓ 使用选择器 '{selector}' 获取到内容")
                            break
                    except:
                        continue

                # 如果没有找到特定的内容区域，尝试获取整个页面的文本
                if not email_content:
                    try:
                        body_text = self.driver.find_element(By.TAG_NAME, "body").text
                        # 过滤掉导航和菜单文本，只保留主要内容
                        lines = body_text.split('\n')
                        content_lines = []
                        for line in lines:
                            line = line.strip()
                            if line and len(line) > 10 and not any(nav_word in line.lower() for nav_word in ['菜单', '导航', '返回', '首页', '登录', '注册']):
                                content_lines.append(line)

                        if content_lines:
                            email_content = '\n'.join(content_lines[:20])  # 限制行数
                    except:
                        pass

                # 关闭新窗口并返回原窗口
                current_windows_after = self.driver.window_handles
                if len(current_windows_after) > 1:
                    # 有新窗口，关闭它
                    for window in current_windows_after:
                        if window != original_window:
                            self.driver.switch_to.window(window)
                            self.driver.close()
                            break
                    self.driver.switch_to.window(original_window)
                else:
                    # 如果没有新窗口，检查URL是否改变
                    current_url = self.driver.current_url
                    if current_url != original_url:
                        # URL改变了，尝试返回
                        try:
                            self.driver.back()
                            time.sleep(2)
                        except:
                            # 如果back失败，直接导航回收件箱
                            try:
                                self.driver.get(original_url)
                                time.sleep(2)
                            except:
                                pass

                if email_content:
                    print(f"  ✓ 成功获取邮件详细内容 ({len(email_content)} 字符)")
                    return email_content[:1000]  # 限制内容长度
                else:
                    print(f"  ✗ 未能获取到邮件详细内容")
                    return None

            except Exception as e:
                print(f"  ✗ 点击邮件失败: {e}")
                # 确保返回原窗口
                try:
                    if len(self.driver.window_handles) > 1:
                        self.driver.switch_to.window(original_window)
                except:
                    pass
                return None

        except Exception as e:
            print(f"获取邮件详细内容失败: {e}")
            return None

    def extract_email_info_fast(self, email_element):
        """快速提取邮件信息（不点击进入详情）"""
        try:
            email_info = {
                'sender': '',
                'subject': '',
                'date': '',
                'content': '',
                'read_status': 'unknown',
                'raw_text': ''
            }

            # 获取元素的原始文本
            raw_text = ""
            try:
                raw_text = email_element.text.strip()
                email_info['raw_text'] = raw_text
            except Exception as e:
                print(f"  获取原始文本失败: {e}")
                raw_text = ""

            # 检查已读状态
            try:
                class_attr = email_element.get_attribute('class') or ''
                if 'unread' in class_attr.lower():
                    email_info['read_status'] = 'unread'
                elif 'read' in class_attr.lower():
                    email_info['read_status'] = 'read'
            except:
                pass

            # 尝试从表格单元格提取信息
            try:
                cells = email_element.find_elements(By.TAG_NAME, "td")
                if len(cells) >= 3:
                    email_info['sender'] = cells[0].text.strip()
                    email_info['subject'] = cells[1].text.strip()
                    email_info['date'] = cells[-1].text.strip()
                    return email_info
            except:
                pass

            # 从原始文本中解析
            if raw_text:
                import re

                # 按行分割文本
                lines = [line.strip() for line in raw_text.split('\n') if line.strip()]

                if len(lines) >= 2:
                    # 第一行通常是发件人
                    first_line = lines[0]
                    email_info['sender'] = first_line

                    # 查找日期（通常在最后一行或包含时间格式的行）
                    time_patterns = [
                        r'今天\s*\d{1,2}:\d{2}',
                        r'昨天\s*\d{1,2}:\d{2}',
                        r'前天\s*\d{1,2}:\d{2}',
                        r'\d{1,2}:\d{2}',
                        r'\d{4}-\d{1,2}-\d{1,2}'
                    ]

                    date_line = ""
                    date_line_index = -1
                    for i, line in enumerate(lines):
                        for pattern in time_patterns:
                            if re.search(pattern, line):
                                email_info['date'] = line
                                date_line = line
                                date_line_index = i
                                break
                        if date_line:
                            break

                    # 提取主题（第二行开始，去掉日期行）
                    subject_lines = []
                    for i, line in enumerate(lines):
                        if i > 0 and i != date_line_index:  # 跳过第一行（发件人）和日期行
                            subject_lines.append(line)

                    if subject_lines:
                        # 合并主题行，但限制长度
                        full_subject = ' '.join(subject_lines)
                        email_info['subject'] = full_subject[:200] if len(full_subject) > 200 else full_subject
                        # 内容是主题的完整版本
                        email_info['content'] = full_subject
                    else:
                        # 如果没有找到主题，使用第二行
                        if len(lines) > 1:
                            email_info['subject'] = lines[1][:100]
                            email_info['content'] = lines[1]

                elif len(lines) == 1:
                    # 只有一行的情况，尝试解析
                    line = lines[0]
                    # 检查是否包含邮箱地址
                    email_match = re.search(r'[\w\.-]+@[\w\.-]+\.\w+', line)
                    if email_match:
                        email_info['sender'] = email_match.group()
                        # 剩余部分作为主题
                        remaining = line.replace(email_match.group(), '').strip()
                        if remaining:
                            email_info['subject'] = remaining[:100]
                            email_info['content'] = remaining
                    else:
                        email_info['subject'] = line[:100]
                        email_info['content'] = line

                # 如果还是没有找到发件人，尝试在整个文本中查找邮箱
                if not email_info['sender']:
                    email_match = re.search(r'[\w\.-]+@[\w\.-]+\.\w+', raw_text)
                    if email_match:
                        email_info['sender'] = email_match.group()

                # 如果还是没有找到日期，使用默认模式
                if not email_info['date']:
                    for pattern in time_patterns:
                        time_match = re.search(pattern, raw_text)
                        if time_match:
                            email_info['date'] = time_match.group()
                            break

            # 确保至少有一些信息
            if not email_info['sender'] and not email_info['subject'] and raw_text:
                # 如果都没有提取到，至少把原始文本的前100个字符作为主题
                email_info['subject'] = raw_text[:100]
                email_info['content'] = raw_text

            return email_info if email_info['sender'] or email_info['subject'] else None

        except Exception as e:
            print(f"快速提取邮件信息失败: {e}")
            return None

    def save_emails(self, format='json'):
        """保存邮件到文件"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            if format == 'json':
                filename = f"emails_2925_{timestamp}.json"
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(self.emails, f, ensure_ascii=False, indent=2)
                print(f"邮件已保存到 {filename}")

            elif format == 'csv':
                filename = f"emails_2925_{timestamp}.csv"
                with open(filename, 'w', newline='', encoding='utf-8') as f:
                    if self.emails:
                        writer = csv.DictWriter(f, fieldnames=self.emails[0].keys())
                        writer.writeheader()
                        writer.writerows(self.emails)
                print(f"邮件已保存到 {filename}")

            return filename

        except Exception as e:
            print(f"保存邮件失败: {e}")
            return None

    def close(self):
        """关闭浏览器"""
        if self.driver:
            try:
                self.driver.quit()
                print("浏览器已关闭")
            except Exception as e:
                print(f"关闭浏览器时出现错误（可忽略）: {e}")
                try:
                    # 强制结束进程
                    import psutil
                    import os
                    current_pid = os.getpid()
                    for proc in psutil.process_iter(['pid', 'name']):
                        if 'msedge' in proc.info['name'].lower() or 'chrome' in proc.info['name'].lower():
                            if proc.info['pid'] != current_pid:
                                try:
                                    proc.terminate()
                                except:
                                    pass
                except:
                    pass

def main():
    """主函数"""
    crawler = Email2925Crawler()

    try:
        # 初始化浏览器驱动
        if not crawler.setup_driver():
            return

        # 执行登录
        if not crawler.login():
            print("登录失败，无法继续爬取邮件")
            return

        # 默认使用详细模式获取完整邮件内容
        print("\n🔍 使用详细模式爬取邮件（获取完整内容）...")
        use_detail_mode = True

        # 执行详细模式爬取
        success = crawler.crawl_emails_with_content(3)

        if success:
            # 保存邮件
            json_file = crawler.save_emails('json')

            print(f"\n{'='*50}")
            print(f"📧 最新3条邮件摘要")
            print(f"{'='*50}")

            for i, email in enumerate(crawler.emails, 1):
                print(f"\n📨 第{i}封邮件:")
                print(f"   👤 发件人: {email['sender']}")
                print(f"   📝 主题: {email['subject']}")
                print(f"   📅 日期: {email['date']}")
                print(f"   📖 状态: {email['read_status']}")
                if email.get('has_detail'):
                    print(f"   📄 内容预览: {email['content'][:100]}...")
                elif email['content']:
                    print(f"   📄 内容: {email['content'][:100]}...")
                print(f"   {'-'*40}")

            print(f"\n💾 详细信息已保存到: {json_file}")
            print(f"✅ 共获取 {len(crawler.emails)} 封邮件")
        else:
            print("❌ 邮件爬取失败")

    except KeyboardInterrupt:
        print("\n用户中断操作")
    except Exception as e:
        print(f"程序执行出错: {e}")
    finally:
        crawler.close()

if __name__ == "__main__":
    main()
