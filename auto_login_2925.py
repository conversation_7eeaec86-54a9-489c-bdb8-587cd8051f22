#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
2925邮箱自动登录和快速邮件提取脚本
自动填写邮箱账号密码并登录，然后快速提取最新5条邮件
"""

import time
import os
import json
import re
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.edge.service import Service as EdgeService
from selenium.webdriver.edge.options import Options as EdgeOptions
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.microsoft import EdgeChromiumDriverManager
import config

class Email2925FastCrawler:
    def __init__(self):
        self.login_url = config.LOGIN_URL
        self.email = config.EMAIL
        self.password = config.PASSWORD
        self.driver = None
        self.emails = []

    def setup_driver(self, headless=None):
        """设置Edge浏览器驱动"""
        if headless is None:
            headless = config.HEADLESS_MODE

        try:
            print("正在初始化Edge浏览器...")
            edge_options = EdgeOptions()
            if headless:
                edge_options.add_argument("--headless")

            # 添加更多稳定性选项
            edge_options.add_argument("--no-sandbox")
            edge_options.add_argument("--disable-dev-shm-usage")
            edge_options.add_argument("--disable-gpu")
            edge_options.add_argument("--disable-web-security")
            edge_options.add_argument("--disable-features=VizDisplayCompositor")
            edge_options.add_argument("--ignore-certificate-errors")
            edge_options.add_argument("--ignore-ssl-errors")
            edge_options.add_argument("--ignore-certificate-errors-spki-list")
            edge_options.add_argument("--disable-extensions")
            edge_options.add_argument("--disable-plugins")
            edge_options.add_argument("--disable-images")  # 加快加载速度
            edge_options.add_argument(f"--window-size={config.WINDOW_SIZE}")

            # 设置用户代理
            edge_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0")

            # 智能获取Edge驱动 - 优先使用缓存
            try:
                # EdgeChromiumDriverManager会自动检查缓存，只在需要时下载
                driver_manager = EdgeChromiumDriverManager()
                driver_path = driver_manager.install()

                # 检查驱动文件是否存在且可执行
                if os.path.exists(driver_path) and os.access(driver_path, os.X_OK):
                    print(f"使用Edge驱动: {driver_path}")
                else:
                    print("驱动文件不可用，重新获取...")
                    driver_path = EdgeChromiumDriverManager().install()
                    print(f"重新获取Edge驱动: {driver_path}")

            except Exception as e:
                print(f"获取Edge驱动失败: {e}")
                return False

            service = EdgeService(driver_path)
            self.driver = webdriver.Edge(service=service, options=edge_options)
            self.driver.implicitly_wait(config.IMPLICIT_WAIT)
            print("Edge浏览器驱动初始化成功")
            return True
        except Exception as e:
            print(f"Edge浏览器驱动初始化失败: {e}")
            print("可能的解决方案:")
            print("1. 确保已安装Edge浏览器")
            print("2. 尝试更新Edge浏览器到最新版本")
            print("3. 检查系统架构是否匹配")
            return False

    def is_browser_alive(self):
        """检查浏览器是否仍然活跃"""
        try:
            # 尝试获取当前窗口句柄
            self.driver.current_window_handle
            return True
        except Exception:
            return False

    def login(self):
        """执行自动登录"""
        try:
            print("正在打开登录页面...")

            # 设置页面加载超时
            self.driver.set_page_load_timeout(30)

            # 尝试多次加载页面
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    print(f"尝试加载页面 (第{attempt + 1}次)...")
                    self.driver.get(self.login_url)
                    break
                except Exception as e:
                    print(f"页面加载失败 (第{attempt + 1}次): {e}")
                    if attempt == max_retries - 1:
                        raise Exception("页面加载失败，已达到最大重试次数")
                    time.sleep(2)

            # 等待页面加载完成
            print("等待页面加载完成...")
            try:
                WebDriverWait(self.driver, config.EXPLICIT_WAIT).until(
                    EC.presence_of_element_located((By.TAG_NAME, "body"))
                )
                print("页面主体加载完成")

                # 额外等待确保页面完全渲染
                time.sleep(3)

                # 检查页面是否正确加载
                if "2925" not in self.driver.title and "登录" not in self.driver.title:
                    print(f"警告：页面标题可能不正确: {self.driver.title}")

            except TimeoutException:
                print("页面加载超时，但继续尝试...")
                time.sleep(2)

            # 检查浏览器是否仍然活跃
            if not self.is_browser_alive():
                raise Exception("浏览器窗口已关闭")

            print("页面加载完成，开始分析页面结构...")

            # 添加调试信息 - 打印页面标题和URL
            print(f"页面标题: {self.driver.title}")
            print(f"当前URL: {self.driver.current_url}")

            # 等待页面完全加载
            time.sleep(2)

            # 调试：打印所有输入框信息
            try:
                all_inputs = self.driver.find_elements(By.TAG_NAME, "input")
                print(f"页面中找到 {len(all_inputs)} 个输入框:")
                for i, inp in enumerate(all_inputs):
                    input_type = inp.get_attribute("type") or "text"
                    input_name = inp.get_attribute("name") or "无名称"
                    input_id = inp.get_attribute("id") or "无ID"
                    input_placeholder = inp.get_attribute("placeholder") or "无占位符"
                    input_class = inp.get_attribute("class") or "无class"
                    print(f"  输入框{i+1}: type={input_type}, name={input_name}, id={input_id}, placeholder={input_placeholder}, class={input_class}")
            except Exception as e:
                print(f"调试信息获取失败: {e}")

            print("开始填写登录信息...")

            # 查找邮箱输入框 - 使用更全面的选择器策略
            email_input = None
            email_selectors = [
                "input[type='email']",
                "input[name='email']",
                "input[name='username']",
                "input[name='account']",
                "input[placeholder*='邮箱']",
                "input[placeholder*='用户名']",
                "input[placeholder*='账号']",
                "input[id*='email']",
                "input[id*='username']",
                "input[id*='account']",
                "input[class*='email']",
                "input[class*='username']",
                "input[class*='account']"
            ]

            for selector in email_selectors:
                try:
                    email_input = WebDriverWait(self.driver, 3).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                    )
                    print(f"找到邮箱输入框: {selector}")
                    break
                except TimeoutException:
                    continue

            if not email_input:
                # 如果找不到特定的邮箱输入框，尝试找第一个可见的文本输入框
                try:
                    text_inputs = self.driver.find_elements(By.CSS_SELECTOR, "input[type='text'], input:not([type])")
                    for inp in text_inputs:
                        if inp.is_displayed() and inp.is_enabled():
                            email_input = inp
                            print("使用第一个可见的文本输入框作为邮箱输入框")
                            break
                    if not email_input:
                        raise Exception("无法找到可用的邮箱输入框")
                except Exception:
                    raise Exception("无法找到邮箱输入框")

            # 检查浏览器状态
            if not self.is_browser_alive():
                raise Exception("浏览器窗口在输入邮箱前已关闭")

            # 滚动到邮箱输入框并确保可见
            self.driver.execute_script("arguments[0].scrollIntoView(true);", email_input)
            time.sleep(1)

            # 清空并输入邮箱
            try:
                email_input.clear()
                email_input.click()  # 先点击获得焦点
                time.sleep(0.5)
                email_input.send_keys(self.email)
                print(f"已输入邮箱: {self.email}")
            except Exception as e:
                print(f"输入邮箱失败: {e}")
                # 尝试使用JavaScript输入
                self.driver.execute_script(f"arguments[0].value = '{self.email}';", email_input)
                print("使用JavaScript输入邮箱")

            # 查找密码输入框
            password_input = None
            try:
                password_input = WebDriverWait(self.driver, 10).until(
                    EC.element_to_be_clickable((By.CSS_SELECTOR, "input[type='password']"))
                )
                print("找到密码输入框")
            except TimeoutException:
                raise Exception("无法找到密码输入框")

            # 滚动到密码输入框并确保可见
            self.driver.execute_script("arguments[0].scrollIntoView(true);", password_input)
            time.sleep(1)

            # 清空并输入密码
            try:
                password_input.clear()
                password_input.click()  # 先点击获得焦点
                time.sleep(0.5)
                password_input.send_keys(self.password)
                print("已输入密码")
            except Exception as e:
                print(f"输入密码失败: {e}")
                # 尝试使用JavaScript输入
                self.driver.execute_script(f"arguments[0].value = '{self.password}';", password_input)
                print("使用JavaScript输入密码")

            # 查找并点击复选框
            try:
                print("正在处理复选框...")

                # 根据页面分析，有两个复选框需要处理
                # 1. "30天内免登录" 复选框 (id=ipt_isreturn)
                # 2. 服务协议复选框

                # 处理"30天内免登录"复选框
                try:
                    remember_checkbox = self.driver.find_element(By.ID, "ipt_isreturn")
                    if remember_checkbox.is_displayed() and remember_checkbox.is_enabled():
                        self.driver.execute_script("arguments[0].scrollIntoView(true);", remember_checkbox)
                        time.sleep(0.5)

                        if not remember_checkbox.is_selected():
                            try:
                                remember_checkbox.click()
                                print("已勾选30天内免登录")
                            except Exception as e:
                                print(f"点击30天内免登录复选框失败，尝试JavaScript: {e}")
                                self.driver.execute_script("arguments[0].click();", remember_checkbox)
                                print("使用JavaScript勾选30天内免登录")
                        else:
                            print("30天内免登录复选框已经勾选")
                    else:
                        print("30天内免登录复选框不可用")
                except Exception as e:
                    print(f"处理30天内免登录复选框失败: {e}")

                # 处理服务协议复选框
                try:
                    # 尝试多种方式找到服务协议复选框
                    agreement_selectors = [
                        "input[type='checkbox']:not(#ipt_isreturn)",  # 排除已处理的复选框
                        ".agreement input[type='checkbox']",
                        ".terms input[type='checkbox']",
                        "input[name*='agreement']",
                        "input[name*='terms']",
                        "input[id*='agreement']",
                        "input[id*='terms']"
                    ]

                    agreement_checkbox = None
                    for selector in agreement_selectors:
                        try:
                            checkboxes = self.driver.find_elements(By.CSS_SELECTOR, selector)
                            for cb in checkboxes:
                                if cb.is_displayed() and cb.is_enabled():
                                    agreement_checkbox = cb
                                    break
                            if agreement_checkbox:
                                break
                        except:
                            continue

                    # 如果没找到特定的服务协议复选框，查找所有复选框
                    if not agreement_checkbox:
                        all_checkboxes = self.driver.find_elements(By.CSS_SELECTOR, "input[type='checkbox']")
                        for cb in all_checkboxes:
                            if cb.get_attribute("id") != "ipt_isreturn" and cb.is_displayed() and cb.is_enabled():
                                agreement_checkbox = cb
                                print("找到可能的服务协议复选框")
                                break

                    if agreement_checkbox:
                        self.driver.execute_script("arguments[0].scrollIntoView(true);", agreement_checkbox)
                        time.sleep(0.5)

                        if not agreement_checkbox.is_selected():
                            try:
                                agreement_checkbox.click()
                                print("已勾选服务协议")
                            except Exception as e:
                                print(f"点击服务协议复选框失败，尝试JavaScript: {e}")
                                self.driver.execute_script("arguments[0].click();", agreement_checkbox)
                                print("使用JavaScript勾选服务协议")
                        else:
                            print("服务协议复选框已经勾选")
                    else:
                        print("未找到服务协议复选框")

                except Exception as e:
                    print(f"处理服务协议复选框失败: {e}")

            except Exception as e:
                print(f"处理复选框时出错: {e}")

            # 查找登录按钮并点击
            login_button = None
            login_selectors = [
                "button[type='submit']",
                "input[type='submit']",
                ".login-btn",
                "#login-btn",
                "button.btn",
                ".btn-login",
                ".submit-btn",
                "button[class*='login']",
                "button[class*='submit']",
                "input[class*='login']",
                "input[class*='submit']"
            ]

            # 首先尝试CSS选择器
            for selector in login_selectors:
                try:
                    buttons = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for btn in buttons:
                        if btn.is_displayed() and btn.is_enabled():
                            login_button = btn
                            print(f"找到登录按钮: {selector}")
                            break
                    if login_button:
                        break
                except:
                    continue

            # 如果CSS选择器没找到，尝试XPath
            if not login_button:
                xpath_selectors = [
                    "//button[contains(text(), '登录')]",
                    "//input[@value='登录']",
                    "//button[contains(text(), '登陆')]",
                    "//input[@value='登陆']",
                    "//button[contains(text(), 'Login')]",
                    "//input[@value='Login']",
                    "//button[contains(@class, 'login')]",
                    "//input[contains(@class, 'login')]"
                ]

                for xpath in xpath_selectors:
                    try:
                        buttons = self.driver.find_elements(By.XPATH, xpath)
                        for btn in buttons:
                            if btn.is_displayed() and btn.is_enabled():
                                login_button = btn
                                print(f"通过XPath找到登录按钮: {xpath}")
                                break
                        if login_button:
                            break
                    except:
                        continue

            if not login_button:
                # 最后尝试：查找所有按钮和输入框，看看有没有可能的登录按钮
                all_buttons = self.driver.find_elements(By.TAG_NAME, "button") + self.driver.find_elements(By.CSS_SELECTOR, "input[type='submit'], input[type='button']")
                print(f"页面中找到 {len(all_buttons)} 个按钮:")
                for i, btn in enumerate(all_buttons):
                    btn_text = btn.text or btn.get_attribute("value") or "无文本"
                    btn_class = btn.get_attribute("class") or "无class"
                    btn_id = btn.get_attribute("id") or "无ID"
                    print(f"  按钮{i+1}: text='{btn_text}', class='{btn_class}', id='{btn_id}'")
                    if "登录" in btn_text or "登陆" in btn_text or "login" in btn_text.lower():
                        login_button = btn
                        print(f"根据文本内容选择按钮{i+1}作为登录按钮")
                        break

                if not login_button:
                    raise Exception("无法找到登录按钮")

            # 滚动到登录按钮并点击
            self.driver.execute_script("arguments[0].scrollIntoView(true);", login_button)
            time.sleep(1)

            print("准备点击登录按钮...")
            try:
                login_button.click()
                print("已点击登录按钮")
            except Exception as e:
                print(f"点击登录按钮失败，尝试JavaScript: {e}")
                self.driver.execute_script("arguments[0].click();", login_button)
                print("使用JavaScript点击登录按钮")

            # 等待登录完成 - 检查是否跳转到邮箱主页
            print("正在等待登录完成...")
            time.sleep(config.LOGIN_WAIT)

            # 检查是否登录成功
            current_url = self.driver.current_url
            if "login" not in current_url.lower():
                print("登录成功！")
                print(f"当前页面: {current_url}")
                return True
            else:
                # 检查是否有错误信息
                try:
                    error_elements = self.driver.find_elements(By.CSS_SELECTOR, ".error, .alert-danger, .warning")
                    if error_elements:
                        error_text = error_elements[0].text
                        print(f"登录失败: {error_text}")
                    else:
                        print("登录可能失败，仍在登录页面")
                except:
                    print("无法确定登录状态")
                return False

        except Exception as e:
            print(f"登录过程中出现错误: {e}")

            # 尝试获取更多调试信息
            try:
                if self.driver:
                    print(f"当前URL: {self.driver.current_url}")
                    print(f"页面标题: {self.driver.title}")

                    # 检查是否有错误信息
                    try:
                        error_elements = self.driver.find_elements(By.CSS_SELECTOR, ".error, .alert, .warning, [class*='error'], [class*='alert']")
                        if error_elements:
                            for elem in error_elements:
                                if elem.is_displayed():
                                    print(f"页面错误信息: {elem.text}")
                    except:
                        pass
            except:
                print("无法获取调试信息")

            return False

    def extract_latest_emails(self, count=5):
        """快速提取最新的邮件信息"""
        try:
            print(f"开始提取最新 {count} 条邮件...")

            # 等待页面加载完成
            time.sleep(3)

            # 查找邮件列表
            print("查找邮件列表...")
            email_rows = self.driver.find_elements(By.CSS_SELECTOR, "tbody tr")

            if not email_rows:
                print("未找到邮件列表，尝试其他选择器...")
                # 尝试其他可能的选择器
                selectors = [
                    ".mail-list tr",
                    ".email-list .email-item",
                    ".message-list .message-item",
                    "table tr",
                    ".list-item"
                ]

                for selector in selectors:
                    email_rows = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if email_rows:
                        print(f"使用选择器 '{selector}' 找到 {len(email_rows)} 个邮件")
                        break

            if not email_rows:
                print("无法找到邮件列表")
                return []

            print(f"找到 {len(email_rows)} 个邮件元素，提取前 {count} 个...")

            # 限制提取数量
            email_rows = email_rows[:count]

            extracted_emails = []

            for i, row in enumerate(email_rows):
                try:
                    print(f"提取第 {i+1} 封邮件...")

                    # 获取行的所有文本内容
                    row_text = row.text.strip()

                    if not row_text:
                        print(f"第 {i+1} 封邮件无文本内容，跳过")
                        continue

                    # 解析邮件信息
                    email_info = self.parse_email_row(row, row_text)

                    if email_info:
                        extracted_emails.append(email_info)
                        print(f"成功提取: {email_info['sender']} - {email_info['subject'][:50]}...")
                    else:
                        print(f"第 {i+1} 封邮件解析失败")

                except Exception as e:
                    print(f"处理第 {i+1} 封邮件时出错: {e}")
                    continue

            self.emails = extracted_emails
            print(f"邮件提取完成，共获取 {len(extracted_emails)} 封邮件")
            return extracted_emails

        except Exception as e:
            print(f"提取邮件失败: {e}")
            return []

    def parse_email_row(self, row_element, row_text):
        """解析单行邮件信息"""
        try:
            email_info = {
                'sender': '',
                'subject': '',
                'date': '',
                'content': '',
                'read_status': 'unknown',
                'raw_text': row_text
            }

            # 尝试从元素属性中获取信息
            try:
                # 检查是否已读/未读
                class_attr = row_element.get_attribute('class') or ''
                if 'unread' in class_attr.lower():
                    email_info['read_status'] = 'unread'
                elif 'read' in class_attr.lower():
                    email_info['read_status'] = 'read'
            except:
                pass

            # 尝试查找具体的邮件信息元素
            try:
                # 查找发件人
                sender_elements = row_element.find_elements(By.XPATH, ".//td[1] | .//div[1] | .//*[contains(@class, 'sender')]")
                if sender_elements:
                    sender_text = sender_elements[0].text.strip()
                    if sender_text:
                        email_info['sender'] = sender_text

                # 查找主题
                subject_elements = row_element.find_elements(By.XPATH, ".//td[2] | .//div[2] | .//*[contains(@class, 'subject')]")
                if subject_elements:
                    subject_text = subject_elements[0].text.strip()
                    if subject_text:
                        email_info['subject'] = subject_text

                # 查找日期
                date_elements = row_element.find_elements(By.XPATH, ".//td[last()] | .//div[last()] | .//*[contains(@class, 'date')]")
                if date_elements:
                    date_text = date_elements[-1].text.strip()
                    if date_text and ('今天' in date_text or '昨天' in date_text or ':' in date_text or '-' in date_text):
                        email_info['date'] = date_text

            except Exception as e:
                print(f"从元素中提取信息失败: {e}")

            # 如果从元素中没有获取到信息，尝试从文本中解析
            if not email_info['sender'] and not email_info['subject']:
                self.parse_from_text(email_info, row_text)

            return email_info

        except Exception as e:
            print(f"解析邮件行失败: {e}")
            return None

    def parse_from_text(self, email_info, text):
        """从文本中解析邮件信息"""
        try:
            # 使用正则表达式查找邮箱地址
            email_pattern = r'[\w\.-]+@[\w\.-]+\.\w+'
            email_matches = re.findall(email_pattern, text)
            if email_matches:
                email_info['sender'] = email_matches[0]

            # 查找时间信息
            time_patterns = [
                r'今天\s*\d{1,2}:\d{2}',
                r'昨天\s*\d{1,2}:\d{2}',
                r'\d{1,2}:\d{2}',
                r'\d{4}-\d{1,2}-\d{1,2}',
                r'\d{1,2}-\d{1,2}'
            ]

            for pattern in time_patterns:
                time_match = re.search(pattern, text)
                if time_match:
                    email_info['date'] = time_match.group()
                    break

            # 如果没有找到发件人，使用文本的第一部分作为主题
            if not email_info['sender'] and text:
                # 取前100个字符作为主题
                email_info['subject'] = text[:100]

        except Exception as e:
            print(f"从文本解析失败: {e}")

    def save_emails_fast(self):
        """快速保存邮件到JSON文件"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"latest_emails_2925_{timestamp}.json"

            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.emails, f, ensure_ascii=False, indent=2)

            print(f"邮件已保存到 {filename}")
            return filename

        except Exception as e:
            print(f"保存邮件失败: {e}")
            return None

    def keep_browser_open(self):
        """保持浏览器打开状态"""
        print("浏览器将保持打开状态，按 Ctrl+C 退出...")
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n正在关闭浏览器...")
            self.close()

    def close(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()
            print("浏览器已关闭")

def main():
    """主函数"""
    crawler = Email2925FastCrawler()

    try:
        # 初始化浏览器驱动
        if not crawler.setup_driver():
            return

        # 执行登录
        if crawler.login():
            print("自动登录完成！")

            # 快速提取最新5条邮件
            emails = crawler.extract_latest_emails(5)

            if emails:
                # 保存邮件
                filename = crawler.save_emails_fast()

                print(f"\n=== 最新5条邮件摘要 ===")
                for i, email in enumerate(emails, 1):
                    print(f"\n第{i}封邮件:")
                    print(f"  发件人: {email['sender']}")
                    print(f"  主题: {email['subject'][:80]}...")
                    print(f"  日期: {email['date']}")
                    print(f"  状态: {email['read_status']}")

                print(f"\n详细信息已保存到: {filename}")
            else:
                print("未能提取到邮件信息")

            # 询问是否保持浏览器打开
            print("\n是否保持浏览器打开？(y/n): ", end="")
            try:
                choice = input().lower()
                if choice == 'y':
                    crawler.keep_browser_open()
                else:
                    crawler.close()
            except:
                crawler.close()
        else:
            print("自动登录失败")
            time.sleep(5)  # 等待5秒让用户看到结果

    except KeyboardInterrupt:
        print("\n用户中断操作")
    except Exception as e:
        print(f"程序执行出错: {e}")
    finally:
        crawler.close()

if __name__ == "__main__":
    main()
