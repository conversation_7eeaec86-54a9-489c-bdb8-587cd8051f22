#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Augment Code 注册测试脚本
自动化测试Augment注册流程
"""

import time
import random
import string
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.edge.service import Service as EdgeService
from selenium.webdriver.edge.options import Options as EdgeOptions
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.microsoft import EdgeChromiumDriverManager
import config

class AugmentRegisterTest:
    def __init__(self):
        self.base_url = "https://www.augmentcode.com/"
        self.driver = None
        self.test_email = self.generate_test_email()

    def generate_test_email(self):
        """生成测试邮箱地址"""
        # 生成4个随机字符（数字和字母）
        random_chars = ''.join(random.choices(string.ascii_lowercase + string.digits, k=4))
        # 组合邮箱地址
        email = f"{config.EMAIL}{random_chars}@2925.com"
        print(f"生成的测试邮箱: {email}")
        return email

    def setup_driver(self):
        """设置Edge浏览器驱动"""
        try:
            print("正在初始化Edge浏览器...")
            edge_options = EdgeOptions()

            # 基本选项 + GPU错误修复
            edge_options.add_argument("--no-sandbox")
            edge_options.add_argument("--disable-dev-shm-usage")
            edge_options.add_argument("--disable-gpu")
            edge_options.add_argument("--disable-web-security")
            edge_options.add_argument("--ignore-certificate-errors")
            edge_options.add_argument("--disable-extensions")
            edge_options.add_argument("--enable-unsafe-swiftshader")  # 修复WebGL错误
            edge_options.add_argument("--disable-software-rasterizer")
            edge_options.add_argument("--disable-background-timer-throttling")
            edge_options.add_argument("--disable-renderer-backgrounding")
            edge_options.add_argument("--disable-features=TranslateUI")
            edge_options.add_argument("--disable-ipc-flooding-protection")
            edge_options.add_argument(f"--window-size={config.WINDOW_SIZE}")
            edge_options.page_load_strategy = 'eager'

            # 获取Edge驱动
            driver_manager = EdgeChromiumDriverManager()
            driver_path = driver_manager.install()
            print(f"使用Edge驱动: {driver_path}")

            service = EdgeService(driver_path)
            self.driver = webdriver.Edge(service=service, options=edge_options)
            self.driver.implicitly_wait(config.IMPLICIT_WAIT)
            print("Edge浏览器驱动初始化成功")
            return True
        except Exception as e:
            print(f"Edge浏览器驱动初始化失败: {e}")
            return False

    def open_homepage(self):
        """打开Augment主页"""
        try:
            print("正在打开Augment主页...")
            self.driver.set_page_load_timeout(20)
            self.driver.get(self.base_url)

            # 等待页面加载
            WebDriverWait(self.driver, 15).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            time.sleep(3)

            print(f"成功打开主页: {self.driver.current_url}")
            print(f"页面标题: {self.driver.title}")
            return True

        except Exception as e:
            print(f"打开主页失败: {e}")
            return False

    def click_install_now(self):
        """点击Install now按钮"""
        try:
            print("正在查找并点击Install now按钮...")

            # 多种选择器尝试找到Install now按钮
            install_selectors = [
                "//button[contains(text(), 'Install now')]",
                "//a[contains(text(), 'Install now')]",
                "//div[contains(text(), 'Install now')]",
                "//span[contains(text(), 'Install now')]",
                "[data-testid*='install']",
                ".install-button",
                "#install-button",
                "button[class*='install']",
                "a[class*='install']"
            ]

            for selector in install_selectors:
                try:
                    if selector.startswith("//"):
                        element = WebDriverWait(self.driver, 5).until(
                            EC.element_to_be_clickable((By.XPATH, selector))
                        )
                    else:
                        element = WebDriverWait(self.driver, 5).until(
                            EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                        )

                    if element and element.is_displayed():
                        print(f"找到Install now按钮: {selector}")
                        # 滚动到元素可见
                        self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
                        time.sleep(1)
                        element.click()
                        print("✓ 成功点击Install now按钮")
                        time.sleep(3)
                        return True

                except Exception as e:
                    continue

            print("❌ 未找到Install now按钮")
            return False

        except Exception as e:
            print(f"点击Install now按钮失败: {e}")
            return False

    def fill_email_and_continue(self):
        """填写邮箱并点击Continue按钮"""
        try:
            print("正在填写邮箱地址...")

            # 等待注册页面加载
            time.sleep(5)
            print(f"当前页面URL: {self.driver.current_url}")

            # 先处理可能存在的checkbox
            self.handle_checkboxes()

            # 查找邮箱输入框 - 更全面的选择器
            email_selectors = [
                "input[type='email']",
                "input[name='email']",
                "input[id='email']",
                "input[name='username']",
                "input[id='username']",
                "input[placeholder*='email' i]",
                "input[placeholder*='Email' i]",
                "input[placeholder*='address' i]",
                "input[data-testid*='email']",
                "input[data-testid*='username']",
                "#email",
                "#username",
                "#identifier",
                ".email-input",
                ".username-input",
                "//input[contains(@placeholder, 'Email')]",
                "//input[contains(@placeholder, 'email')]",
                "//input[contains(@placeholder, 'address')]",
                "//input[contains(@name, 'email')]",
                "//input[contains(@name, 'username')]",
                "//input[contains(@id, 'email')]",
                "//input[contains(@id, 'username')]"
            ]

            # 先打印页面信息用于调试
            print("页面调试信息:")
            try:
                page_source_snippet = self.driver.page_source[:1000]
                print(f"页面源码片段: {page_source_snippet}...")
            except:
                pass

            # 查找所有input元素进行调试
            try:
                all_inputs = self.driver.find_elements(By.TAG_NAME, "input")
                print(f"页面上共找到 {len(all_inputs)} 个input元素:")
                for i, inp in enumerate(all_inputs[:10]):  # 只显示前10个
                    try:
                        input_type = inp.get_attribute('type') or 'text'
                        input_name = inp.get_attribute('name') or ''
                        input_id = inp.get_attribute('id') or ''
                        input_placeholder = inp.get_attribute('placeholder') or ''
                        print(f"  Input {i+1}: type='{input_type}', name='{input_name}', id='{input_id}', placeholder='{input_placeholder}'")
                    except:
                        pass
            except:
                pass

            email_input = None
            for selector in email_selectors:
                try:
                    if selector.startswith("//"):
                        elements = self.driver.find_elements(By.XPATH, selector)
                    else:
                        elements = self.driver.find_elements(By.CSS_SELECTOR, selector)

                    for element in elements:
                        if element and element.is_displayed() and element.is_enabled():
                            email_input = element
                            print(f"找到邮箱输入框: {selector}")
                            break

                    if email_input:
                        break
                except Exception as e:
                    print(f"选择器 {selector} 失败: {e}")
                    continue

            if not email_input:
                print("❌ 未找到邮箱输入框")
                print("尝试查找第一个可见的input元素...")
                try:
                    all_inputs = self.driver.find_elements(By.TAG_NAME, "input")
                    for inp in all_inputs:
                        if inp.is_displayed() and inp.is_enabled():
                            input_type = inp.get_attribute('type') or 'text'
                            if input_type in ['text', 'email', '']:
                                email_input = inp
                                print(f"使用第一个可见的文本输入框")
                                break
                except:
                    pass

                if not email_input:
                    return False

            # 清空并输入邮箱
            email_input.clear()
            email_input.send_keys(self.test_email)
            print(f"✓ 已输入邮箱: {self.test_email}")
            time.sleep(1)

            # 查找并点击Continue按钮
            continue_selectors = [
                "//button[contains(text(), 'Continue')]",
                "//button[contains(text(), 'continue')]",
                "//input[@type='submit']",
                "//button[@type='submit']",
                "button[class*='continue']",
                "button[class*='submit']",
                ".continue-button",
                "#continue-button"
            ]

            for selector in continue_selectors:
                try:
                    if selector.startswith("//"):
                        continue_btn = WebDriverWait(self.driver, 5).until(
                            EC.element_to_be_clickable((By.XPATH, selector))
                        )
                    else:
                        continue_btn = WebDriverWait(self.driver, 5).until(
                            EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                        )

                    if continue_btn and continue_btn.is_displayed():
                        print(f"找到Continue按钮: {selector}")
                        continue_btn.click()
                        print("✓ 成功点击Continue按钮")
                        time.sleep(3)
                        return True

                except:
                    continue

            print("❌ 未找到Continue按钮")
            return False

        except Exception as e:
            print(f"填写邮箱和点击Continue失败: {e}")
            return False

    def handle_checkboxes(self):
        """处理页面上的checkbox"""
        try:
            print("检查并处理checkbox...")

            # 查找各种类型的checkbox
            checkbox_selectors = [
                "input[type='checkbox']",
                ".checkbox",
                ".icheckbox_minimal",
                ".iCheck-helper",
                "//input[@type='checkbox']",
                "//div[contains(@class, 'checkbox')]",
                "//div[contains(@class, 'icheckbox')]",
                "//span[contains(@class, 'checkbox')]",
                "[role='checkbox']"
            ]

            checkboxes_found = 0
            for selector in checkbox_selectors:
                try:
                    if selector.startswith("//"):
                        elements = self.driver.find_elements(By.XPATH, selector)
                    else:
                        elements = self.driver.find_elements(By.CSS_SELECTOR, selector)

                    for element in elements:
                        try:
                            if element.is_displayed() and element.is_enabled():
                                # 检查是否已经选中
                                is_checked = element.is_selected() if element.tag_name == 'input' else 'checked' in element.get_attribute('class')

                                if not is_checked:
                                    # 尝试点击checkbox
                                    try:
                                        element.click()
                                        checkboxes_found += 1
                                        print(f"✓ 已点击checkbox: {selector}")
                                        time.sleep(0.5)
                                    except:
                                        # 如果直接点击失败，尝试JavaScript点击
                                        try:
                                            self.driver.execute_script("arguments[0].click();", element)
                                            checkboxes_found += 1
                                            print(f"✓ 已通过JS点击checkbox: {selector}")
                                            time.sleep(0.5)
                                        except:
                                            pass
                        except:
                            continue
                except:
                    continue

            if checkboxes_found > 0:
                print(f"✓ 总共处理了 {checkboxes_found} 个checkbox")
            else:
                print("未找到需要处理的checkbox")

        except Exception as e:
            print(f"处理checkbox失败: {e}")

    def check_verification_page(self):
        """检查是否到达验证码输入页面"""
        try:
            print("检查是否到达验证码页面...")
            time.sleep(3)

            current_url = self.driver.current_url
            page_title = self.driver.title
            print(f"当前URL: {current_url}")
            print(f"页面标题: {page_title}")

            # 查找验证码相关元素
            verification_indicators = [
                "//input[contains(@placeholder, 'code')]",
                "//input[contains(@placeholder, 'Code')]",
                "//input[contains(@placeholder, '验证码')]",
                "//div[contains(text(), 'verification')]",
                "//div[contains(text(), 'code')]",
                "//div[contains(text(), 'email')]",
                "input[placeholder*='code' i]",
                "input[name*='code' i]",
                ".verification-code",
                "#verification-code"
            ]

            for selector in verification_indicators:
                try:
                    if selector.startswith("//"):
                        element = self.driver.find_element(By.XPATH, selector)
                    else:
                        element = self.driver.find_element(By.CSS_SELECTOR, selector)

                    if element and element.is_displayed():
                        print(f"✓ 找到验证码相关元素: {selector}")
                        print("✅ 成功到达验证码输入页面！")
                        return True
                except:
                    continue

            # 检查页面文本内容
            page_text = self.driver.find_element(By.TAG_NAME, "body").text.lower()
            if any(keyword in page_text for keyword in ['code', 'verification', 'verify', 'email']):
                print("✅ 页面内容显示已到达验证码页面！")
                return True

            print("❌ 未确认到达验证码页面")
            return False

        except Exception as e:
            print(f"检查验证码页面失败: {e}")
            return False

    def run_test(self):
        """运行完整的注册测试"""
        try:
            print("🚀 开始Augment注册测试...")
            print(f"测试邮箱: {self.test_email}")
            print("="*50)

            # 1. 初始化浏览器
            if not self.setup_driver():
                return False

            # 2. 打开主页
            if not self.open_homepage():
                return False

            # 3. 点击Install now按钮
            if not self.click_install_now():
                return False

            # 4. 填写邮箱并点击Continue
            if not self.fill_email_and_continue():
                return False

            # 5. 检查是否到达验证码页面
            if not self.check_verification_page():
                return False

            print("\n" + "="*50)
            print("🎉 Augment注册测试完成！")
            print(f"✅ 成功使用邮箱 {self.test_email} 进行注册")
            print("✅ 已到达验证码输入页面")
            print("💡 接下来需要手动输入验证码完成注册")

            # 保持浏览器打开，等待用户手动操作
            input("\n按Enter键关闭浏览器...")

            return True

        except Exception as e:
            print(f"注册测试失败: {e}")
            return False
        finally:
            self.close()

    def close(self):
        """关闭浏览器"""
        if self.driver:
            try:
                self.driver.quit()
                print("浏览器已关闭")
            except Exception as e:
                print(f"关闭浏览器时出现错误（可忽略）: {e}")

def main():
    """主函数"""
    tester = AugmentRegisterTest()
    tester.run_test()

if __name__ == "__main__":
    main()
