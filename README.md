# 2925邮箱自动登录工具

这是一个自动登录2925邮箱的Python脚本，使用Selenium自动化浏览器操作。

## 功能特点

- 自动填写邮箱账号和密码
- 自动勾选"记住登录状态"复选框
- 自动点击登录按钮
- 登录成功后保持浏览器打开状态
- 支持多种元素定位策略，提高成功率

## 安装依赖

首先确保您已安装Python 3.7+，然后安装所需依赖：

```bash
pip install -r requirements.txt
```

## 使用方法

### 方法1：直接运行Python脚本

```bash
python auto_login_2925.py
```

### 方法2：使用批处理文件（Windows）

双击运行 `run_auto_login.bat` 文件，或在命令行中执行：

```cmd
run_auto_login.bat
```

### 方法3：测试配置

运行配置测试脚本来验证设置：

```bash
python test_config.py
```

## 配置信息

脚本中已预设了以下登录信息：
- 邮箱：<EMAIL>
- 密码：edward2wong
- 登录页面：https://www.2925.com/login/

如需修改，请编辑脚本中的相应变量。

## 注意事项

1. 首次运行时，webdriver-manager会自动下载Chrome驱动程序
2. 确保您的系统已安装Chrome浏览器
3. 脚本运行时会打开浏览器窗口，登录成功后会保持打开状态
4. 按Ctrl+C可以退出程序并关闭浏览器

## 故障排除

如果登录失败，可能的原因：
1. 网络连接问题
2. 2925邮箱网站结构发生变化
3. 账号或密码错误
4. Chrome浏览器版本不兼容

## 依赖项

- selenium: Web自动化框架
- webdriver-manager: 自动管理浏览器驱动程序
