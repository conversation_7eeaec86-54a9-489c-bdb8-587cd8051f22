#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
2925邮箱自动登录脚本 - 专门处理 iCheck 复选框
"""

import time
import os
import json
import re
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.edge.service import Service as EdgeService
from selenium.webdriver.edge.options import Options as EdgeOptions
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.microsoft import EdgeChromiumDriverManager
import config

class ICheckEmail2925Login:
    def __init__(self):
        self.login_url = config.LOGIN_URL
        self.email = config.EMAIL
        self.password = config.PASSWORD
        self.driver = None
        self.emails = []

    def setup_driver(self):
        """设置Edge浏览器驱动"""
        try:
            print("正在初始化Edge浏览器...")
            edge_options = EdgeOptions()

            # 基本选项
            edge_options.add_argument("--no-sandbox")
            edge_options.add_argument("--disable-dev-shm-usage")
            edge_options.add_argument("--disable-gpu")
            edge_options.add_argument("--disable-web-security")
            edge_options.add_argument("--ignore-certificate-errors")
            edge_options.add_argument("--disable-extensions")
            edge_options.add_argument(f"--window-size={config.WINDOW_SIZE}")
            edge_options.page_load_strategy = 'eager'

            # 获取Edge驱动
            driver_manager = EdgeChromiumDriverManager()
            driver_path = driver_manager.install()
            print(f"使用Edge驱动: {driver_path}")

            service = EdgeService(driver_path)
            self.driver = webdriver.Edge(service=service, options=edge_options)
            self.driver.implicitly_wait(config.IMPLICIT_WAIT)
            print("Edge浏览器驱动初始化成功")
            return True
        except Exception as e:
            print(f"Edge浏览器驱动初始化失败: {e}")
            return False

    def handle_icheck_checkbox(self, checkbox_id, description):
        """处理 iCheck 复选框的通用方法"""
        try:
            print(f"处理{description}复选框...")

            # 查找原始复选框
            original_checkbox = self.driver.find_element(By.ID, checkbox_id)
            print(f"{description}复选框状态: visible={original_checkbox.is_displayed()}, enabled={original_checkbox.is_enabled()}, selected={original_checkbox.is_selected()}")

            if original_checkbox.is_selected():
                print(f"{description}已经勾选")
                return True

            success = False

            # 方法1: 查找并点击 iCheck 包装器
            try:
                # 查找包含 icheckbox_minimal 类的元素
                icheck_wrappers = self.driver.find_elements(By.CSS_SELECTOR, ".icheckbox_minimal")
                print(f"找到 {len(icheck_wrappers)} 个 iCheck 包装器")

                for i, wrapper in enumerate(icheck_wrappers):
                    try:
                        # 检查这个包装器是否与我们的复选框相关
                        # 通过查找包装器内部或附近的复选框
                        related_checkbox = None
                        try:
                            related_checkbox = wrapper.find_element(By.XPATH, f".//input[@id='{checkbox_id}']")
                        except:
                            try:
                                related_checkbox = wrapper.find_element(By.XPATH, f"./following-sibling::input[@id='{checkbox_id}'] | ./preceding-sibling::input[@id='{checkbox_id}']")
                            except:
                                pass

                        if related_checkbox or i == 0:  # 如果找到相关复选框，或者是第一个包装器
                            print(f"尝试点击第{i+1}个iCheck包装器")

                            # 滚动到包装器
                            self.driver.execute_script("arguments[0].scrollIntoView(true);", wrapper)
                            time.sleep(0.5)

                            # 尝试点击
                            try:
                                wrapper.click()
                                success = True
                                print(f"通过iCheck包装器勾选{description}")
                                break
                            except Exception as e:
                                print(f"点击包装器失败: {e}")
                                try:
                                    self.driver.execute_script("arguments[0].click();", wrapper)
                                    success = True
                                    print(f"使用JavaScript点击iCheck包装器勾选{description}")
                                    break
                                except Exception as e2:
                                    print(f"JavaScript点击包装器也失败: {e2}")
                                    continue
                    except Exception as e:
                        print(f"处理第{i+1}个包装器失败: {e}")
                        continue

            except Exception as e:
                print(f"查找iCheck包装器失败: {e}")

            # 方法2: 如果iCheck方式失败，尝试直接操作复选框
            if not success:
                print(f"尝试直接操作{description}复选框...")
                try:
                    # 使用JavaScript强制勾选
                    self.driver.execute_script("arguments[0].checked = true;", original_checkbox)
                    self.driver.execute_script("arguments[0].dispatchEvent(new Event('change', { bubbles: true }));", original_checkbox)

                    # 如果页面使用了iCheck，尝试调用iCheck的API
                    self.driver.execute_script("""
                        if (typeof $ !== 'undefined' && $(arguments[0]).iCheck) {
                            $(arguments[0]).iCheck('check');
                        }
                    """, original_checkbox)

                    success = True
                    print(f"通过JavaScript直接勾选{description}")
                except Exception as e:
                    print(f"JavaScript直接操作失败: {e}")

            # 方法3: 尝试查找并点击相关的label
            if not success:
                try:
                    labels = self.driver.find_elements(By.XPATH, f"//label[@for='{checkbox_id}']")
                    for label in labels:
                        try:
                            label.click()
                            success = True
                            print(f"通过label勾选{description}")
                            break
                        except:
                            continue
                except Exception as e:
                    print(f"点击label失败: {e}")

            # 检查最终状态
            time.sleep(0.5)
            final_state = original_checkbox.is_selected()
            print(f"{description}最终状态: selected={final_state}")

            return final_state

        except Exception as e:
            print(f"处理{description}复选框失败: {e}")
            return False

    def login(self):
        """执行自动登录"""
        try:
            print("正在打开登录页面...")
            self.driver.set_page_load_timeout(20)
            self.driver.get(self.login_url)

            # 等待页面加载
            WebDriverWait(self.driver, 15).until(
                EC.presence_of_element_located((By.ID, "ipt_account"))
            )
            time.sleep(3)

            print(f"页面标题: {self.driver.title}")
            print(f"当前URL: {self.driver.current_url}")

            # 输入邮箱
            print("正在输入邮箱...")
            email_input = self.driver.find_element(By.ID, "ipt_account")
            email_input.clear()
            email_input.send_keys(self.email)
            print(f"已输入邮箱: {self.email}")

            # 输入密码
            print("正在输入密码...")
            password_input = self.driver.find_element(By.ID, "ipt_password")
            password_input.clear()
            password_input.send_keys(self.password)
            print("已输入密码")

            # 处理复选框
            print("正在处理复选框...")

            # 处理30天内免登录复选框
            self.handle_icheck_checkbox("ipt_isreturn", "30天内免登录")

            # 查找并处理服务协议复选框
            try:
                print("查找服务协议复选框...")
                all_checkboxes = self.driver.find_elements(By.CSS_SELECTOR, "input[type='checkbox']")
                print(f"页面总共有 {len(all_checkboxes)} 个复选框")

                agreement_handled = False
                for i, checkbox in enumerate(all_checkboxes):
                    checkbox_id = checkbox.get_attribute("id") or f"checkbox_{i}"
                    checkbox_name = checkbox.get_attribute("name") or "无name"
                    print(f"复选框{i+1}: id={checkbox_id}, name={checkbox_name}")

                    if checkbox_id != "ipt_isreturn":
                        print(f"处理服务协议复选框: {checkbox_id}")
                        result = self.handle_icheck_checkbox(checkbox_id, "服务协议")
                        if result:
                            agreement_handled = True
                        break

                # 如果没有找到其他复选框，尝试直接处理第二个iCheck包装器
                if not agreement_handled:
                    print("没有找到其他复选框，尝试直接处理第二个iCheck包装器...")
                    try:
                        icheck_wrappers = self.driver.find_elements(By.CSS_SELECTOR, ".icheckbox_minimal")
                        if len(icheck_wrappers) >= 2:
                            second_wrapper = icheck_wrappers[1]
                            print("尝试点击第二个iCheck包装器（服务协议）")

                            self.driver.execute_script("arguments[0].scrollIntoView(true);", second_wrapper)
                            time.sleep(0.5)

                            try:
                                second_wrapper.click()
                                print("通过第二个iCheck包装器勾选服务协议")
                            except Exception as e:
                                print(f"点击第二个包装器失败: {e}")
                                try:
                                    self.driver.execute_script("arguments[0].click();", second_wrapper)
                                    print("使用JavaScript点击第二个iCheck包装器勾选服务协议")
                                except Exception as e2:
                                    print(f"JavaScript点击第二个包装器也失败: {e2}")
                        else:
                            print(f"只找到 {len(icheck_wrappers)} 个iCheck包装器，无法处理第二个")
                    except Exception as e:
                        print(f"处理第二个iCheck包装器失败: {e}")

            except Exception as e:
                print(f"查找服务协议复选框失败: {e}")

            # 点击登录按钮
            print("正在点击登录按钮...")
            login_button = self.driver.find_element(By.ID, "btn_login")
            self.driver.execute_script("arguments[0].scrollIntoView(true);", login_button)
            time.sleep(0.5)

            try:
                login_button.click()
                print("已点击登录按钮")
            except Exception as e:
                print(f"常规点击失败，尝试JavaScript点击: {e}")
                self.driver.execute_script("arguments[0].click();", login_button)
                print("使用JavaScript点击登录按钮")

            # 等待登录完成
            print("等待登录完成...")
            time.sleep(5)

            # 检查登录结果
            current_url = self.driver.current_url
            if "login" not in current_url.lower():
                print("登录成功！")
                print(f"当前页面: {current_url}")
                return True
            else:
                print("登录可能失败，仍在登录页面")
                return False

        except Exception as e:
            print(f"登录过程中出现错误: {e}")
            return False

    def extract_latest_emails_fast(self, count=3):
        """快速提取最新的邮件信息"""
        try:
            print(f"\n开始快速提取最新 {count} 条邮件...")

            # 等待邮箱主页加载
            time.sleep(3)

            # 查找邮件列表 - 使用多种策略
            print("查找邮件列表...")
            email_elements = []

            # 策略1: 查找表格行
            try:
                rows = self.driver.find_elements(By.CSS_SELECTOR, "tbody tr")
                if rows and len(rows) > 1:  # 排除表头
                    email_elements = rows[1:count+1]  # 跳过表头，取前count条
                    print(f"通过表格行找到 {len(email_elements)} 个邮件")
            except:
                pass

            # 策略2: 如果没找到，尝试其他选择器
            if not email_elements:
                selectors = [
                    ".mail-list .mail-item",
                    ".email-list .email-item",
                    ".message-list .message-item",
                    "table tr:not(:first-child)",
                    ".list-item"
                ]

                for selector in selectors:
                    try:
                        elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                        if elements:
                            email_elements = elements[:count]
                            print(f"使用选择器 '{selector}' 找到 {len(email_elements)} 个邮件")
                            break
                    except:
                        continue

            if not email_elements:
                print("未找到邮件列表")
                return []

            print(f"开始提取 {len(email_elements)} 封邮件的信息...")
            extracted_emails = []

            for i, element in enumerate(email_elements):
                try:
                    print(f"提取第 {i+1} 封邮件...")

                    # 快速提取邮件信息，避免点击进入详情
                    email_info = self.parse_email_element_fast(element, i+1)

                    if email_info:
                        extracted_emails.append(email_info)
                        print(f"✓ 发件人: {email_info['sender'][:30]}... | 主题: {email_info['subject'][:40]}...")
                    else:
                        print(f"✗ 第 {i+1} 封邮件解析失败")

                except Exception as e:
                    print(f"✗ 处理第 {i+1} 封邮件时出错: {e}")
                    continue

            self.emails = extracted_emails
            print(f"\n邮件提取完成！共获取 {len(extracted_emails)} 封邮件")
            return extracted_emails

        except Exception as e:
            print(f"提取邮件失败: {e}")
            return []

    def parse_email_element_fast(self, element, index):
        """快速解析邮件元素信息"""
        try:
            email_info = {
                'index': index,
                'sender': '',
                'subject': '',
                'date': '',
                'read_status': 'unknown',
                'raw_text': ''
            }

            # 获取元素的所有文本
            try:
                raw_text = element.text.strip()
                email_info['raw_text'] = raw_text
            except:
                pass

            # 检查已读/未读状态
            try:
                class_attr = element.get_attribute('class') or ''
                if 'unread' in class_attr.lower() or 'new' in class_attr.lower():
                    email_info['read_status'] = 'unread'
                elif 'read' in class_attr.lower():
                    email_info['read_status'] = 'read'
            except:
                pass

            # 尝试从表格单元格中提取信息
            try:
                cells = element.find_elements(By.TAG_NAME, "td")
                if len(cells) >= 3:
                    # 通常邮件列表的格式是: 发件人 | 主题 | 日期
                    email_info['sender'] = cells[0].text.strip()
                    email_info['subject'] = cells[1].text.strip()
                    email_info['date'] = cells[-1].text.strip()  # 最后一列通常是日期
                    return email_info
            except:
                pass

            # 如果不是表格格式，尝试从div或其他元素中提取
            try:
                divs = element.find_elements(By.TAG_NAME, "div")
                if len(divs) >= 2:
                    email_info['sender'] = divs[0].text.strip()
                    email_info['subject'] = divs[1].text.strip()
                    if len(divs) >= 3:
                        email_info['date'] = divs[-1].text.strip()
                    return email_info
            except:
                pass

            # 最后尝试从原始文本中解析
            if raw_text:
                self.parse_from_raw_text(email_info, raw_text)

            return email_info if email_info['sender'] or email_info['subject'] else None

        except Exception as e:
            print(f"解析邮件元素失败: {e}")
            return None

    def parse_from_raw_text(self, email_info, text):
        """从原始文本中解析邮件信息"""
        try:
            # 使用正则表达式查找邮箱地址
            email_pattern = r'[\w\.-]+@[\w\.-]+\.\w+'
            email_matches = re.findall(email_pattern, text)
            if email_matches:
                email_info['sender'] = email_matches[0]

            # 查找时间信息
            time_patterns = [
                r'今天\s*\d{1,2}:\d{2}',
                r'昨天\s*\d{1,2}:\d{2}',
                r'\d{1,2}:\d{2}',
                r'\d{4}-\d{1,2}-\d{1,2}',
                r'\d{1,2}-\d{1,2}'
            ]

            for pattern in time_patterns:
                time_match = re.search(pattern, text)
                if time_match:
                    email_info['date'] = time_match.group()
                    break

            # 如果找到了发件人，尝试提取主题（去掉发件人和日期后的内容）
            if email_info['sender']:
                subject_text = text.replace(email_info['sender'], '').strip()
                if email_info['date']:
                    subject_text = subject_text.replace(email_info['date'], '').strip()
                if subject_text:
                    email_info['subject'] = subject_text[:100]  # 限制长度
            else:
                # 如果没有找到发件人，使用前50个字符作为主题
                email_info['subject'] = text[:50]

        except Exception as e:
            print(f"从原始文本解析失败: {e}")

    def save_emails_json(self):
        """保存邮件到JSON文件"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"latest_3_emails_2925_{timestamp}.json"

            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.emails, f, ensure_ascii=False, indent=2)

            print(f"邮件已保存到: {filename}")
            return filename

        except Exception as e:
            print(f"保存邮件失败: {e}")
            return None

    def keep_browser_open(self):
        """保持浏览器打开状态"""
        print("浏览器将保持打开状态，按 Ctrl+C 退出...")
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n正在关闭浏览器...")
            self.close()

    def close(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()
            print("浏览器已关闭")

def main():
    """主函数"""
    login = ICheckEmail2925Login()

    try:
        # 初始化浏览器驱动
        if not login.setup_driver():
            return

        # 执行登录
        if login.login():
            print("自动登录完成！")

            # 快速提取最新3条邮件
            emails = login.extract_latest_emails_fast(3)

            if emails:
                # 保存邮件
                filename = login.save_emails_json()

                print(f"\n{'='*50}")
                print(f"📧 最新3条邮件摘要")
                print(f"{'='*50}")

                for email in emails:
                    print(f"\n📨 第{email['index']}封邮件:")
                    print(f"   👤 发件人: {email['sender']}")
                    print(f"   📝 主题: {email['subject']}")
                    print(f"   📅 日期: {email['date']}")
                    print(f"   📖 状态: {email['read_status']}")
                    print(f"   {'-'*40}")

                print(f"\n💾 详细信息已保存到: {filename}")
                print(f"✅ 共提取 {len(emails)} 封邮件")
            else:
                print("❌ 未能提取到邮件信息")

            # 询问是否保持浏览器打开
            print(f"\n{'='*50}")
            print("🌐 是否保持浏览器打开以便查看更多邮件？")
            print("输入 'y' 保持打开，其他任意键关闭浏览器")
            try:
                choice = input("请选择: ").lower().strip()
                if choice == 'y':
                    print("浏览器将保持打开状态...")
                    login.keep_browser_open()
                else:
                    print("正在关闭浏览器...")
                    login.close()
            except:
                print("正在关闭浏览器...")
                login.close()
        else:
            print("自动登录失败")
            time.sleep(5)  # 等待5秒让用户看到结果

    except KeyboardInterrupt:
        print("\n用户中断操作")
    except Exception as e:
        print(f"程序执行出错: {e}")
    finally:
        login.close()

if __name__ == "__main__":
    main()
